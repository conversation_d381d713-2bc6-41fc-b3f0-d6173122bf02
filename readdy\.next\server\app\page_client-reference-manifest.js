globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./components/ErrorBoundary.tsx":{"*":{"id":"(ssr)/./components/ErrorBoundary.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/GlobalUI.tsx":{"*":{"id":"(ssr)/./components/GlobalUI.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./contexts/AuthContext.tsx":{"*":{"id":"(ssr)/./contexts/AuthContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/page.tsx":{"*":{"id":"(ssr)/./app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/../node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/../node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/../node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/../node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/../node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/../node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/../node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/../node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/login/page.tsx":{"*":{"id":"(ssr)/./app/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/register/page.tsx":{"*":{"id":"(ssr)/./app/register/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Desktop\\bingxiang\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Pacifico\",\"arguments\":[{\"weight\":\"400\",\"subsets\":[\"latin\"],\"display\":\"swap\",\"variable\":\"--font-pacifico\"}],\"variableName\":\"pacifico\"}":{"id":"(app-pages-browser)/../node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Pacifico\",\"arguments\":[{\"weight\":\"400\",\"subsets\":[\"latin\"],\"display\":\"swap\",\"variable\":\"--font-pacifico\"}],\"variableName\":\"pacifico\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\bingxiang\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"}":{"id":"(app-pages-browser)/../node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\bingxiang\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"}":{"id":"(app-pages-browser)/../node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\bingxiang\\readdy\\app\\globals.css":{"id":"(app-pages-browser)/./app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\bingxiang\\readdy\\components\\ErrorBoundary.tsx":{"id":"(app-pages-browser)/./components/ErrorBoundary.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\bingxiang\\readdy\\components\\GlobalUI.tsx":{"id":"(app-pages-browser)/./components/GlobalUI.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\bingxiang\\readdy\\contexts\\AuthContext.tsx":{"id":"(app-pages-browser)/./contexts/AuthContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\bingxiang\\readdy\\app\\page.tsx":{"id":"(app-pages-browser)/./app/page.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\bingxiang\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\bingxiang\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\bingxiang\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\bingxiang\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\bingxiang\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\bingxiang\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\bingxiang\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\bingxiang\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\bingxiang\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\bingxiang\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\bingxiang\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\bingxiang\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\bingxiang\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\bingxiang\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\bingxiang\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\bingxiang\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\bingxiang\\readdy\\app\\login\\page.tsx":{"id":"(app-pages-browser)/./app/login/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\bingxiang\\readdy\\app\\register\\page.tsx":{"id":"(app-pages-browser)/./app/register/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Desktop\\bingxiang\\readdy\\":[],"C:\\Users\\<USER>\\Desktop\\bingxiang\\readdy\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"C:\\Users\\<USER>\\Desktop\\bingxiang\\readdy\\app\\not-found":[],"C:\\Users\\<USER>\\Desktop\\bingxiang\\readdy\\app\\page":[]},"rscModuleMapping":{"(app-pages-browser)/./app/globals.css":{"*":{"id":"(rsc)/./app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/ErrorBoundary.tsx":{"*":{"id":"(rsc)/./components/ErrorBoundary.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/GlobalUI.tsx":{"*":{"id":"(rsc)/./components/GlobalUI.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./contexts/AuthContext.tsx":{"*":{"id":"(rsc)/./contexts/AuthContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/page.tsx":{"*":{"id":"(rsc)/./app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/../node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/../node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/../node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/../node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/../node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/../node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/../node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/../node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/login/page.tsx":{"*":{"id":"(rsc)/./app/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/register/page.tsx":{"*":{"id":"(rsc)/./app/register/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{"(app-pages-browser)/../node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/../node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/../node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/../node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/../node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/../node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/../node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/../node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/../node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}}}}