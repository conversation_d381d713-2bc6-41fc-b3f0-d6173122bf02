/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/login/page";
exports.ids = ["app/login/page"];
exports.modules = {

/***/ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5Cjames%5CDesktop%5Cbingxiang%5Creaddy%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjames%5CDesktop%5Cbingxiang%5Creaddy&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5Cjames%5CDesktop%5Cbingxiang%5Creaddy%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjames%5CDesktop%5Cbingxiang%5Creaddy&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/module.compiled.js?fc76\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/../node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/../node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/../node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/not-found.tsx */ \"(rsc)/./app/not-found.tsx\"));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/../node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/../node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/login/page.tsx */ \"(rsc)/./app/login/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'login',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\login\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\not-found.tsx\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\login\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/login/page\",\n        pathname: \"/login\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5Cjames%5CDesktop%5Cbingxiang%5Creaddy%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjames%5CDesktop%5Cbingxiang%5Creaddy&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/client-page.js */ \"(rsc)/../node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/../node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/../node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/../node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/../node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/../node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/../node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/../node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Pacifico%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22weight%5C%22%3A%5C%22400%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-pacifico%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22pacifico%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Creaddy%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Creaddy%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Creaddy%5C%5Ccomponents%5C%5CGlobalUI.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Creaddy%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Pacifico%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22weight%5C%22%3A%5C%22400%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-pacifico%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22pacifico%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Creaddy%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Creaddy%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Creaddy%5C%5Ccomponents%5C%5CGlobalUI.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Creaddy%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ErrorBoundary.tsx */ \"(rsc)/./components/ErrorBoundary.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/GlobalUI.tsx */ \"(rsc)/./components/GlobalUI.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./contexts/AuthContext.tsx */ \"(rsc)/./contexts/AuthContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Pacifico%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22weight%5C%22%3A%5C%22400%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-pacifico%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22pacifico%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Creaddy%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Creaddy%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Creaddy%5C%5Ccomponents%5C%5CGlobalUI.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Creaddy%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Creaddy%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Creaddy%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/login/page.tsx */ \"(rsc)/./app/login/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNqYW1lcyU1QyU1Q0Rlc2t0b3AlNUMlNUNiaW5neGlhbmclNUMlNUNyZWFkZHklNUMlNUNhcHAlNUMlNUNsb2dpbiU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvSkFBdUciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXGphbWVzXFxcXERlc2t0b3BcXFxcYmluZ3hpYW5nXFxcXHJlYWRkeVxcXFxhcHBcXFxcbG9naW5cXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Creaddy%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!*******************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \*******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"816be40f0325\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcamFtZXNcXERlc2t0b3BcXGJpbmd4aWFuZ1xccmVhZGR5XFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiODE2YmU0MGYwMzI1XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/../node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Pacifico_arguments_weight_400_subsets_latin_display_swap_variable_font_pacifico_variableName_pacifico___WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Pacifico\",\"arguments\":[{\"weight\":\"400\",\"subsets\":[\"latin\"],\"display\":\"swap\",\"variable\":\"--font-pacifico\"}],\"variableName\":\"pacifico\"} */ \"(rsc)/../node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Pacifico\\\",\\\"arguments\\\":[{\\\"weight\\\":\\\"400\\\",\\\"subsets\\\":[\\\"latin\\\"],\\\"display\\\":\\\"swap\\\",\\\"variable\\\":\\\"--font-pacifico\\\"}],\\\"variableName\\\":\\\"pacifico\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Pacifico_arguments_weight_400_subsets_latin_display_swap_variable_font_pacifico_variableName_pacifico___WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Pacifico_arguments_weight_400_subsets_latin_display_swap_variable_font_pacifico_variableName_pacifico___WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/../node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/../node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(rsc)/./contexts/AuthContext.tsx\");\n/* harmony import */ var _components_GlobalUI__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/GlobalUI */ \"(rsc)/./components/GlobalUI.tsx\");\n/* harmony import */ var _components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ErrorBoundary */ \"(rsc)/./components/ErrorBoundary.tsx\");\n\n\n\n\n\n\n\n\nconst metadata = {\n    title: \"智能冰箱助手 - Readdy\",\n    description: \"AI智能食材识别和管理系统\"\n};\nconst viewport = {\n    width: 'device-width',\n    initialScale: 1,\n    maximumScale: 1,\n    userScalable: false\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        suppressHydrationWarning: true,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                    href: \"https://cdn.jsdelivr.net/npm/remixicon@4.0.0/fonts/remixicon.css\",\n                    rel: \"stylesheet\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\layout.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\layout.tsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_5___default().variable)} ${(next_font_google_target_css_path_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_6___default().variable)} ${(next_font_google_target_css_path_app_layout_tsx_import_Pacifico_arguments_weight_400_subsets_latin_display_swap_variable_font_pacifico_variableName_pacifico___WEBPACK_IMPORTED_MODULE_7___default().variable)} antialiased`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                        children: [\n                            children,\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_GlobalUI__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\layout.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\layout.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\layout.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\layout.tsx\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\layout.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBT01BO0FBT0FDO0FBS0FDO0FBakJpQjtBQUMrQjtBQUNUO0FBQ1U7QUFtQmhELE1BQU1JLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFFO0FBRUssTUFBTUMsV0FBVztJQUN0QkMsT0FBTztJQUNQQyxjQUFjO0lBQ2RDLGNBQWM7SUFDZEMsY0FBYztBQUNoQixFQUFFO0FBRWEsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdSO0lBQ0EscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7UUFBUUMsMEJBQTBCOzswQkFDM0MsOERBQUNDOzBCQUNDLDRFQUFDQztvQkFBS0MsTUFBSztvQkFBbUVDLEtBQUk7Ozs7Ozs7Ozs7OzBCQUVwRiw4REFBQ0M7Z0JBQ0NDLFdBQVcsR0FBR3ZCLHVMQUFrQixDQUFDLENBQUMsRUFBRUMsNExBQWtCLENBQUMsQ0FBQyxFQUFFRiwrTUFBaUIsQ0FBQyxZQUFZLENBQUM7MEJBRXpGLDRFQUFDSyxpRUFBYUE7OEJBQ1osNEVBQUNGLCtEQUFZQTs7NEJBQ1ZZOzBDQUNELDhEQUFDWCw0REFBUUE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU1yQiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxqYW1lc1xcRGVza3RvcFxcYmluZ3hpYW5nXFxyZWFkZHlcXGFwcFxcbGF5b3V0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSBcIm5leHRcIjtcbmltcG9ydCB7IEdlaXN0LCBHZWlzdF9Nb25vLCBQYWNpZmljbyB9IGZyb20gXCJuZXh0L2ZvbnQvZ29vZ2xlXCI7XG5pbXBvcnQgXCIuL2dsb2JhbHMuY3NzXCI7XG5pbXBvcnQgeyBBdXRoUHJvdmlkZXIgfSBmcm9tICdAL2NvbnRleHRzL0F1dGhDb250ZXh0JztcbmltcG9ydCBHbG9iYWxVSSBmcm9tICdAL2NvbXBvbmVudHMvR2xvYmFsVUknO1xuaW1wb3J0IEVycm9yQm91bmRhcnkgZnJvbSAnQC9jb21wb25lbnRzL0Vycm9yQm91bmRhcnknO1xuXG5jb25zdCBwYWNpZmljbyA9IFBhY2lmaWNvKHtcbiAgd2VpZ2h0OiAnNDAwJyxcbiAgc3Vic2V0czogWydsYXRpbiddLFxuICBkaXNwbGF5OiAnc3dhcCcsXG4gIHZhcmlhYmxlOiAnLS1mb250LXBhY2lmaWNvJyxcbn0pXG5cbmNvbnN0IGdlaXN0U2FucyA9IEdlaXN0KHtcbiAgdmFyaWFibGU6IFwiLS1mb250LWdlaXN0LXNhbnNcIixcbiAgc3Vic2V0czogW1wibGF0aW5cIl0sXG59KTtcblxuY29uc3QgZ2Vpc3RNb25vID0gR2Vpc3RfTW9ubyh7XG4gIHZhcmlhYmxlOiBcIi0tZm9udC1nZWlzdC1tb25vXCIsXG4gIHN1YnNldHM6IFtcImxhdGluXCJdLFxufSk7XG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiBcIuaZuuiDveWGsOeuseWKqeaJiyAtIFJlYWRkeVwiLFxuICBkZXNjcmlwdGlvbjogXCJBSeaZuuiDvemjn+adkOivhuWIq+WSjOeuoeeQhuezu+e7n1wiLFxufTtcblxuZXhwb3J0IGNvbnN0IHZpZXdwb3J0ID0ge1xuICB3aWR0aDogJ2RldmljZS13aWR0aCcsXG4gIGluaXRpYWxTY2FsZTogMSxcbiAgbWF4aW11bVNjYWxlOiAxLFxuICB1c2VyU2NhbGFibGU6IGZhbHNlLFxufTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufTogUmVhZG9ubHk8e1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xufT4pIHtcbiAgcmV0dXJuIChcbiAgICA8aHRtbCBsYW5nPVwiemgtQ05cIiBzdXBwcmVzc0h5ZHJhdGlvbldhcm5pbmc9e3RydWV9PlxuICAgICAgPGhlYWQ+XG4gICAgICAgIDxsaW5rIGhyZWY9XCJodHRwczovL2Nkbi5qc2RlbGl2ci5uZXQvbnBtL3JlbWl4aWNvbkA0LjAuMC9mb250cy9yZW1peGljb24uY3NzXCIgcmVsPVwic3R5bGVzaGVldFwiIC8+XG4gICAgICA8L2hlYWQ+XG4gICAgICA8Ym9keVxuICAgICAgICBjbGFzc05hbWU9e2Ake2dlaXN0U2Fucy52YXJpYWJsZX0gJHtnZWlzdE1vbm8udmFyaWFibGV9ICR7cGFjaWZpY28udmFyaWFibGV9IGFudGlhbGlhc2VkYH1cbiAgICAgID5cbiAgICAgICAgPEVycm9yQm91bmRhcnk+XG4gICAgICAgICAgPEF1dGhQcm92aWRlcj5cbiAgICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgICAgIDxHbG9iYWxVSSAvPlxuICAgICAgICAgIDwvQXV0aFByb3ZpZGVyPlxuICAgICAgICA8L0Vycm9yQm91bmRhcnk+XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApO1xufVxuIl0sIm5hbWVzIjpbInBhY2lmaWNvIiwiZ2Vpc3RTYW5zIiwiZ2Vpc3RNb25vIiwiQXV0aFByb3ZpZGVyIiwiR2xvYmFsVUkiLCJFcnJvckJvdW5kYXJ5IiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwidmlld3BvcnQiLCJ3aWR0aCIsImluaXRpYWxTY2FsZSIsIm1heGltdW1TY2FsZSIsInVzZXJTY2FsYWJsZSIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwic3VwcHJlc3NIeWRyYXRpb25XYXJuaW5nIiwiaGVhZCIsImxpbmsiLCJocmVmIiwicmVsIiwiYm9keSIsImNsYXNzTmFtZSIsInZhcmlhYmxlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/login/page.tsx":
/*!****************************!*\
  !*** ./app/login/page.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/../node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\bingxiang\\readdy\\app\\login\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./app/not-found.tsx":
/*!***************************!*\
  !*** ./app/not-found.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/../node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction NotFound() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col items-center justify-center h-screen text-center px-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-5xl md:text-5xl font-semibold text-gray-100\",\n                children: \"404\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\not-found.tsx\",\n                lineNumber: 4,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-2xl md:text-3xl font-semibold mt-6\",\n                children: \"This page has not been generated\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\not-found.tsx\",\n                lineNumber: 5,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mt-4 text-xl md:text-2xl text-gray-500\",\n                children: \"Tell me what you would like on this page\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\not-found.tsx\",\n                lineNumber: 6,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\not-found.tsx\",\n        lineNumber: 3,\n        columnNumber: 7\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbm90LWZvdW5kLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQWUsU0FBU0E7SUFDcEIscUJBQ0UsOERBQUNDO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDQztnQkFBR0QsV0FBVTswQkFBbUQ7Ozs7OzswQkFDakUsOERBQUNDO2dCQUFHRCxXQUFVOzBCQUEwQzs7Ozs7OzBCQUN4RCw4REFBQ0U7Z0JBQUVGLFdBQVU7MEJBQXlDOzs7Ozs7Ozs7Ozs7QUFHNUQiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcamFtZXNcXERlc2t0b3BcXGJpbmd4aWFuZ1xccmVhZGR5XFxhcHBcXG5vdC1mb3VuZC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTm90Rm91bmQoKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgaC1zY3JlZW4gdGV4dC1jZW50ZXIgcHgtNFwiPlxuICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC01eGwgbWQ6dGV4dC01eGwgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktMTAwXCI+NDA0PC9oMT5cbiAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtMnhsIG1kOnRleHQtM3hsIGZvbnQtc2VtaWJvbGQgbXQtNlwiPlRoaXMgcGFnZSBoYXMgbm90IGJlZW4gZ2VuZXJhdGVkPC9oMT5cbiAgICAgICAgPHAgY2xhc3NOYW1lPVwibXQtNCB0ZXh0LXhsIG1kOnRleHQtMnhsIHRleHQtZ3JheS01MDBcIj5UZWxsIG1lIHdoYXQgeW91IHdvdWxkIGxpa2Ugb24gdGhpcyBwYWdlPC9wPlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfSJdLCJuYW1lcyI6WyJOb3RGb3VuZCIsImRpdiIsImNsYXNzTmFtZSIsImgxIiwicCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/not-found.tsx\n");

/***/ }),

/***/ "(rsc)/./components/ErrorBoundary.tsx":
/*!**************************************!*\
  !*** ./components/ErrorBoundary.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/../node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\components\\\\ErrorBoundary.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\bingxiang\\readdy\\components\\ErrorBoundary.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./components/GlobalUI.tsx":
/*!*********************************!*\
  !*** ./components/GlobalUI.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__),
/* harmony export */   useGlobalUI: () => (/* binding */ useGlobalUI)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/../node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const useGlobalUI = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useGlobalUI() from the server but useGlobalUI is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\bingxiang\\readdy\\components\\GlobalUI.tsx",
"useGlobalUI",
);/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\components\\\\GlobalUI.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\bingxiang\\readdy\\components\\GlobalUI.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./contexts/AuthContext.tsx":
/*!**********************************!*\
  !*** ./contexts/AuthContext.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),
/* harmony export */   useAuth: () => (/* binding */ useAuth)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/../node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const useAuth = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\bingxiang\\readdy\\contexts\\AuthContext.tsx",
"useAuth",
);const AuthProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\bingxiang\\readdy\\contexts\\AuthContext.tsx",
"AuthProvider",
);

/***/ }),

/***/ "(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/client-page.js */ \"(ssr)/../node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/../node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/../node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/../node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/../node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/../node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/../node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/../node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Pacifico%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22weight%5C%22%3A%5C%22400%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-pacifico%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22pacifico%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Creaddy%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Creaddy%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Creaddy%5C%5Ccomponents%5C%5CGlobalUI.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Creaddy%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Pacifico%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22weight%5C%22%3A%5C%22400%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-pacifico%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22pacifico%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Creaddy%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Creaddy%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Creaddy%5C%5Ccomponents%5C%5CGlobalUI.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Creaddy%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ErrorBoundary.tsx */ \"(ssr)/./components/ErrorBoundary.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/GlobalUI.tsx */ \"(ssr)/./components/GlobalUI.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./contexts/AuthContext.tsx */ \"(ssr)/./contexts/AuthContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Pacifico%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22weight%5C%22%3A%5C%22400%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-pacifico%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22pacifico%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Creaddy%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Creaddy%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Creaddy%5C%5Ccomponents%5C%5CGlobalUI.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Creaddy%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Creaddy%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Creaddy%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/login/page.tsx */ \"(ssr)/./app/login/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNqYW1lcyU1QyU1Q0Rlc2t0b3AlNUMlNUNiaW5neGlhbmclNUMlNUNyZWFkZHklNUMlNUNhcHAlNUMlNUNsb2dpbiU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvSkFBdUciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXGphbWVzXFxcXERlc2t0b3BcXFxcYmluZ3hpYW5nXFxcXHJlYWRkeVxcXFxhcHBcXFxcbG9naW5cXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Creaddy%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!*******************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \*******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./app/login/page.tsx":
/*!****************************!*\
  !*** ./app/login/page.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoginPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/../node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/../node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../lib/api */ \"(ssr)/./lib/api.ts\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../contexts/AuthContext */ \"(ssr)/./contexts/AuthContext.tsx\");\n/* harmony import */ var _components_GlobalUI__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../components/GlobalUI */ \"(ssr)/./components/GlobalUI.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction LoginPage() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { login } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const { showError, showLoading } = (0,_components_GlobalUI__WEBPACK_IMPORTED_MODULE_6__.useGlobalUI)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        email: \"\",\n        password: \"\"\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!formData.email || !formData.password) {\n            showError(\"请填写所有必填字段\");\n            return;\n        }\n        setLoading(true);\n        showLoading(true);\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.authApi.login({\n                email: formData.email,\n                password: formData.password\n            });\n            if (response.success && response.data) {\n                // 使用 AuthContext 的 login 方法\n                await login(response.data.token, response.data.user);\n                router.push(\"/\");\n            } else {\n                showError(response.error || response.message || \"登录失败，请检查邮箱和密码\");\n            }\n        } catch (error) {\n            console.error(\"登录错误:\", error);\n            showError(\"登录失败，请稍后重试\");\n        } finally{\n            setLoading(false);\n            showLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-blue-100\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                className: \"ri-user-line text-2xl text-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"mt-6 text-center text-3xl font-extrabold text-gray-900\",\n                            children: \"登录您的账户\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-center text-sm text-gray-600\",\n                            children: \"欢迎回到智能冰箱助手\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    className: \"mt-8 space-y-6\",\n                    onSubmit: handleSubmit,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"email\",\n                                            className: \"block text-sm font-medium text-gray-700\",\n                                            children: \"邮箱地址\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 80,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-1 relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"email\",\n                                                    name: \"email\",\n                                                    type: \"email\",\n                                                    autoComplete: \"email\",\n                                                    required: true,\n                                                    className: \"appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\",\n                                                    placeholder: \"请输入邮箱地址\",\n                                                    value: formData.email,\n                                                    onChange: handleChange\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 84,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-y-0 right-0 pr-3 flex items-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                        className: \"ri-mail-line text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 96,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 95,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"password\",\n                                            className: \"block text-sm font-medium text-gray-700\",\n                                            children: \"密码\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-1 relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"password\",\n                                                    name: \"password\",\n                                                    type: \"password\",\n                                                    autoComplete: \"current-password\",\n                                                    required: true,\n                                                    className: \"appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\",\n                                                    placeholder: \"请输入密码\",\n                                                    value: formData.password,\n                                                    onChange: handleChange\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 106,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-y-0 right-0 pr-3 flex items-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                        className: \"ri-lock-line text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 118,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                disabled: loading,\n                                className: \"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200\",\n                                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"登录中...\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"ri-login-circle-line mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"登录\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-gray-600\",\n                                children: [\n                                    \"还没有账户？\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: \"/register\",\n                                        className: \"font-medium text-blue-600 hover:text-blue-500 transition-colors duration-200\",\n                                        children: \"立即注册\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 144,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\login\\\\page.tsx\",\n            lineNumber: 64,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\login\\\\page.tsx\",\n        lineNumber: 63,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/login/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ErrorBoundary.tsx":
/*!**************************************!*\
  !*** ./components/ErrorBoundary.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nclass ErrorBoundary extends react__WEBPACK_IMPORTED_MODULE_1__.Component {\n    constructor(props){\n        super(props);\n        this.state = {\n            hasError: false\n        };\n    }\n    static getDerivedStateFromError(error) {\n        return {\n            hasError: true,\n            error\n        };\n    }\n    componentDidCatch(error, errorInfo) {\n        console.error('ErrorBoundary caught an error:', error, errorInfo);\n        this.setState({\n            error,\n            errorInfo\n        });\n    }\n    render() {\n        if (this.state.hasError) {\n            if (this.props.fallback) {\n                return this.props.fallback;\n            }\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-md w-full bg-white rounded-lg shadow-lg p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mr-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"ri-error-warning-line text-red-600 text-xl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\components\\\\ErrorBoundary.tsx\",\n                                        lineNumber: 42,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\components\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 41,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-lg font-semibold text-gray-900\",\n                                            children: \"出现错误\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\components\\\\ErrorBoundary.tsx\",\n                                            lineNumber: 45,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"应用程序遇到了意外错误\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\components\\\\ErrorBoundary.tsx\",\n                                            lineNumber: 46,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\components\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 44,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 rounded-lg p-4 mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-700 font-mono\",\n                                children: this.state.error?.message || '未知错误'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\components\\\\ErrorBoundary.tsx\",\n                                lineNumber: 51,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>window.location.reload(),\n                                    className: \"flex-1 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\",\n                                    children: \"刷新页面\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\components\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>this.setState({\n                                            hasError: false,\n                                            error: undefined,\n                                            errorInfo: undefined\n                                        }),\n                                    className: \"flex-1 bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors\",\n                                    children: \"重试\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\components\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 13\n                        }, this),\n                         true && this.state.errorInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                            className: \"mt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                    className: \"text-sm text-gray-600 cursor-pointer\",\n                                    children: \"技术详情\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\components\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                    className: \"mt-2 text-xs text-gray-500 bg-gray-100 p-2 rounded overflow-auto max-h-40\",\n                                    children: this.state.errorInfo.componentStack\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\components\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\components\\\\ErrorBoundary.tsx\",\n                lineNumber: 38,\n                columnNumber: 9\n            }, this);\n        }\n        return this.props.children;\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ErrorBoundary);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ErrorBoundary.tsx\n");

/***/ }),

/***/ "(ssr)/./components/GlobalErrorNotification.tsx":
/*!************************************************!*\
  !*** ./components/GlobalErrorNotification.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GlobalErrorNotification)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useApiState__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useApiState */ \"(ssr)/./hooks/useApiState.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction GlobalErrorNotification() {\n    const { error, clearError } = (0,_hooks_useApiState__WEBPACK_IMPORTED_MODULE_2__.useGlobalError)();\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"GlobalErrorNotification.useEffect\": ()=>{\n            if (error) {\n                setIsVisible(true);\n                // 自动隐藏错误通知（5秒后）\n                const timer = setTimeout({\n                    \"GlobalErrorNotification.useEffect.timer\": ()=>{\n                        setIsVisible(false);\n                        setTimeout(clearError, 300); // 等待动画完成后清除错误\n                    }\n                }[\"GlobalErrorNotification.useEffect.timer\"], 5000);\n                return ({\n                    \"GlobalErrorNotification.useEffect\": ()=>clearTimeout(timer)\n                })[\"GlobalErrorNotification.useEffect\"];\n            }\n        }\n    }[\"GlobalErrorNotification.useEffect\"], [\n        error,\n        clearError\n    ]);\n    if (!error) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `fixed top-4 right-4 z-50 transition-all duration-300 ${isVisible ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0'}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-red-50 border border-red-200 rounded-lg shadow-lg p-4 max-w-sm\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-shrink-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                            className: \"ri-error-warning-line text-red-600 text-xl\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\components\\\\GlobalErrorNotification.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\components\\\\GlobalErrorNotification.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"ml-3 flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-sm font-medium text-red-800\",\n                                children: \"操作失败\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\components\\\\GlobalErrorNotification.tsx\",\n                                lineNumber: 37,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-red-700 mt-1\",\n                                children: error.message\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\components\\\\GlobalErrorNotification.tsx\",\n                                lineNumber: 38,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\components\\\\GlobalErrorNotification.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>{\n                            setIsVisible(false);\n                            setTimeout(clearError, 300);\n                        },\n                        className: \"flex-shrink-0 ml-2 text-red-400 hover:text-red-600\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                            className: \"ri-close-line\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\components\\\\GlobalErrorNotification.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\components\\\\GlobalErrorNotification.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\components\\\\GlobalErrorNotification.tsx\",\n                lineNumber: 32,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\components\\\\GlobalErrorNotification.tsx\",\n            lineNumber: 31,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\components\\\\GlobalErrorNotification.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/GlobalErrorNotification.tsx\n");

/***/ }),

/***/ "(ssr)/./components/GlobalLoadingIndicator.tsx":
/*!***********************************************!*\
  !*** ./components/GlobalLoadingIndicator.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GlobalLoadingIndicator)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _hooks_useApiState__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/hooks/useApiState */ \"(ssr)/./hooks/useApiState.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction GlobalLoadingIndicator() {\n    const { isAnyLoading } = (0,_hooks_useApiState__WEBPACK_IMPORTED_MODULE_1__.useGlobalLoading)();\n    if (!isAnyLoading) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed top-0 left-0 right-0 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-1 bg-blue-200\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-full bg-blue-600 animate-pulse w-full\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\components\\\\GlobalLoadingIndicator.tsx\",\n                lineNumber: 15,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\components\\\\GlobalLoadingIndicator.tsx\",\n            lineNumber: 14,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\components\\\\GlobalLoadingIndicator.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL0dsb2JhbExvYWRpbmdJbmRpY2F0b3IudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBRXVEO0FBRXhDLFNBQVNDO0lBQ3RCLE1BQU0sRUFBRUMsWUFBWSxFQUFFLEdBQUdGLG9FQUFnQkE7SUFFekMsSUFBSSxDQUFDRSxjQUFjO1FBQ2pCLE9BQU87SUFDVDtJQUVBLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVO2tCQUNiLDRFQUFDRDtZQUFJQyxXQUFVO3NCQUNiLDRFQUFDRDtnQkFBSUMsV0FBVTs7Ozs7Ozs7Ozs7Ozs7OztBQUl2QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxqYW1lc1xcRGVza3RvcFxcYmluZ3hpYW5nXFxyZWFkZHlcXGNvbXBvbmVudHNcXEdsb2JhbExvYWRpbmdJbmRpY2F0b3IudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcclxuXHJcbmltcG9ydCB7IHVzZUdsb2JhbExvYWRpbmcgfSBmcm9tICdAL2hvb2tzL3VzZUFwaVN0YXRlJztcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEdsb2JhbExvYWRpbmdJbmRpY2F0b3IoKSB7XHJcbiAgY29uc3QgeyBpc0FueUxvYWRpbmcgfSA9IHVzZUdsb2JhbExvYWRpbmcoKTtcclxuXHJcbiAgaWYgKCFpc0FueUxvYWRpbmcpIHtcclxuICAgIHJldHVybiBudWxsO1xyXG4gIH1cclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxkaXYgY2xhc3NOYW1lPVwiZml4ZWQgdG9wLTAgbGVmdC0wIHJpZ2h0LTAgei01MFwiPlxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtMSBiZy1ibHVlLTIwMFwiPlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC1mdWxsIGJnLWJsdWUtNjAwIGFuaW1hdGUtcHVsc2Ugdy1mdWxsXCI+PC9kaXY+XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgPC9kaXY+XHJcbiAgKTtcclxufSJdLCJuYW1lcyI6WyJ1c2VHbG9iYWxMb2FkaW5nIiwiR2xvYmFsTG9hZGluZ0luZGljYXRvciIsImlzQW55TG9hZGluZyIsImRpdiIsImNsYXNzTmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./components/GlobalLoadingIndicator.tsx\n");

/***/ }),

/***/ "(ssr)/./components/GlobalUI.tsx":
/*!*********************************!*\
  !*** ./components/GlobalUI.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GlobalUI),\n/* harmony export */   useGlobalUI: () => (/* binding */ useGlobalUI)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _GlobalLoadingIndicator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./GlobalLoadingIndicator */ \"(ssr)/./components/GlobalLoadingIndicator.tsx\");\n/* harmony import */ var _GlobalErrorNotification__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./GlobalErrorNotification */ \"(ssr)/./components/GlobalErrorNotification.tsx\");\n/* harmony import */ var _hooks_useApiState__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useApiState */ \"(ssr)/./hooks/useApiState.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ useGlobalUI,default auto */ \n\n\n\n\n\n// useGlobalUI Hook - 提供全局UI控制功能\nfunction useGlobalUI() {\n    const { handleError, clearError } = (0,_hooks_useApiState__WEBPACK_IMPORTED_MODULE_4__.useGlobalError)();\n    const { isLoading } = (0,_hooks_useApiState__WEBPACK_IMPORTED_MODULE_4__.useGlobalLoading)();\n    // 显示错误消息\n    const showError = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"useGlobalUI.useCallback[showError]\": (message, details)=>{\n            const error = new Error(message);\n            if (details) {\n                error.details = details;\n            }\n            handleError(error);\n        }\n    }[\"useGlobalUI.useCallback[showError]\"], [\n        handleError\n    ]);\n    // 显示加载状态\n    const showLoading = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"useGlobalUI.useCallback[showLoading]\": (show, key = 'global')=>{\n            if (show) {\n                _lib_api__WEBPACK_IMPORTED_MODULE_5__.loadingManager.setLoading(key, true);\n            } else {\n                _lib_api__WEBPACK_IMPORTED_MODULE_5__.loadingManager.setLoading(key, false);\n            }\n        }\n    }[\"useGlobalUI.useCallback[showLoading]\"], []);\n    // 隐藏错误消息\n    const hideError = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"useGlobalUI.useCallback[hideError]\": ()=>{\n            clearError();\n        }\n    }[\"useGlobalUI.useCallback[hideError]\"], [\n        clearError\n    ]);\n    // 检查是否正在加载\n    const isLoadingState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"useGlobalUI.useCallback[isLoadingState]\": (key)=>{\n            return isLoading(key);\n        }\n    }[\"useGlobalUI.useCallback[isLoadingState]\"], [\n        isLoading\n    ]);\n    return {\n        showError,\n        showLoading,\n        hideError,\n        isLoading: isLoadingState\n    };\n}\nfunction GlobalUI() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_GlobalLoadingIndicator__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\components\\\\GlobalUI.tsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_GlobalErrorNotification__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\components\\\\GlobalUI.tsx\",\n                lineNumber: 54,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/GlobalUI.tsx\n");

/***/ }),

/***/ "(ssr)/./contexts/AuthContext.tsx":
/*!**********************************!*\
  !*** ./contexts/AuthContext.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/api */ \"(ssr)/./lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \n\n\n// 创建认证上下文\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    user: null,\n    token: null,\n    isAuthenticated: false,\n    isLoading: true,\n    login: async ()=>{},\n    logout: ()=>{},\n    refreshUser: async ()=>{}\n});\n// 自定义Hook用于使用认证上下文\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (!context) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n};\n// 认证提供者组件\nconst AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [token, setToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // 计算认证状态\n    const isAuthenticated = !!user && !!token;\n    // 登录函数\n    const login = async (newToken, newUser)=>{\n        try {\n            // 保存到localStorage\n            localStorage.setItem('authToken', newToken);\n            localStorage.setItem('user', JSON.stringify(newUser));\n            // 更新状态\n            setToken(newToken);\n            setUser(newUser);\n            console.log('用户登录成功:', newUser.username);\n        } catch (error) {\n            console.error('登录状态保存失败:', error);\n            throw error;\n        }\n    };\n    // 登出函数\n    const logout = ()=>{\n        try {\n            // 清除localStorage\n            localStorage.removeItem('authToken');\n            localStorage.removeItem('user');\n            // 清除状态\n            setToken(null);\n            setUser(null);\n            console.log('用户已登出');\n        } catch (error) {\n            console.error('登出失败:', error);\n        }\n    };\n    // 刷新用户信息\n    const refreshUser = async ()=>{\n        if (!token) return;\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.authApi.getMe();\n            if (response.success && response.data) {\n                const updatedUser = response.data;\n                setUser(updatedUser);\n                localStorage.setItem('user', JSON.stringify(updatedUser));\n                console.log('用户信息已刷新');\n            } else {\n                console.warn('刷新用户信息失败:', response.error);\n                // 如果获取用户信息失败，可能token已过期，执行登出\n                logout();\n            }\n        } catch (error) {\n            console.error('刷新用户信息错误:', error);\n        // 网络错误等情况下不执行登出，保持当前状态\n        }\n    };\n    // 初始化认证状态\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            const initializeAuth = {\n                \"AuthProvider.useEffect.initializeAuth\": async ()=>{\n                    try {\n                        // 从localStorage恢复认证状态\n                        const savedToken = localStorage.getItem('authToken');\n                        const savedUser = localStorage.getItem('user');\n                        if (savedToken && savedUser) {\n                            try {\n                                const parsedUser = JSON.parse(savedUser);\n                                setToken(savedToken);\n                                setUser(parsedUser);\n                                // 验证token是否仍然有效\n                                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.authApi.getMe();\n                                if (response.success && response.data) {\n                                    // Token有效，更新用户信息\n                                    const currentUser = response.data;\n                                    setUser(currentUser);\n                                    localStorage.setItem('user', JSON.stringify(currentUser));\n                                    console.log('认证状态已恢复:', currentUser.username);\n                                } else {\n                                    // Token无效，清除认证状态\n                                    console.warn('Token已过期，清除认证状态');\n                                    logout();\n                                }\n                            } catch (parseError) {\n                                console.error('解析用户数据失败:', parseError);\n                                logout();\n                            }\n                        } else {\n                            console.log('未找到保存的认证信息');\n                        }\n                    } catch (error) {\n                        console.error('初始化认证状态失败:', error);\n                        // 网络错误等情况下，如果有本地数据就使用本地数据\n                        const savedToken = localStorage.getItem('authToken');\n                        const savedUser = localStorage.getItem('user');\n                        if (savedToken && savedUser) {\n                            try {\n                                const parsedUser = JSON.parse(savedUser);\n                                setToken(savedToken);\n                                setUser(parsedUser);\n                                console.log('使用本地认证数据');\n                            } catch (parseError) {\n                                console.error('解析本地用户数据失败:', parseError);\n                                logout();\n                            }\n                        }\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"AuthProvider.useEffect.initializeAuth\"];\n            initializeAuth();\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    // 定期刷新用户信息（可选）\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            if (!isAuthenticated) return;\n            // 每30分钟刷新一次用户信息\n            const interval = setInterval({\n                \"AuthProvider.useEffect.interval\": ()=>{\n                    refreshUser();\n                }\n            }[\"AuthProvider.useEffect.interval\"], 30 * 60 * 1000);\n            return ({\n                \"AuthProvider.useEffect\": ()=>clearInterval(interval)\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], [\n        isAuthenticated,\n        token\n    ]);\n    const contextValue = {\n        user,\n        token,\n        isAuthenticated,\n        isLoading,\n        login,\n        logout,\n        refreshUser\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 193,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./hooks/useApiState.ts":
/*!******************************!*\
  !*** ./hooks/useApiState.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useApiOperation: () => (/* binding */ useApiOperation),\n/* harmony export */   useApiState: () => (/* binding */ useApiState),\n/* harmony export */   useGlobalError: () => (/* binding */ useGlobalError),\n/* harmony export */   useGlobalLoading: () => (/* binding */ useGlobalLoading)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./lib/api.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/../node_modules/next/dist/api/navigation.js\");\n\n\n\n// 全局错误处理Hook\nfunction useGlobalError() {\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const errorTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    // 自动清除错误（5秒后）\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useGlobalError.useEffect\": ()=>{\n            if (error) {\n                errorTimeoutRef.current = setTimeout({\n                    \"useGlobalError.useEffect\": ()=>{\n                        clearError();\n                    }\n                }[\"useGlobalError.useEffect\"], 5000);\n            }\n            return ({\n                \"useGlobalError.useEffect\": ()=>{\n                    if (errorTimeoutRef.current) {\n                        clearTimeout(errorTimeoutRef.current);\n                    }\n                }\n            })[\"useGlobalError.useEffect\"];\n        }\n    }[\"useGlobalError.useEffect\"], [\n        error\n    ]);\n    const handleError = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useGlobalError.useCallback[handleError]\": (error)=>{\n            // 清除之前的超时\n            if (errorTimeoutRef.current) {\n                clearTimeout(errorTimeoutRef.current);\n                errorTimeoutRef.current = null;\n            }\n            if (error instanceof _lib_api__WEBPACK_IMPORTED_MODULE_1__.ApiError) {\n                setError({\n                    message: error.message,\n                    code: error.code,\n                    status: error.status,\n                    details: error.details,\n                    timestamp: Date.now()\n                });\n                // 特殊处理认证错误\n                if (error.code === 'AUTH_EXPIRED') {\n                    // 重定向到登录页面\n                    console.warn('认证已过期，重定向到登录页面');\n                    router.push('/login');\n                }\n            } else if (error instanceof Error) {\n                setError({\n                    message: error.message,\n                    timestamp: Date.now()\n                });\n            } else {\n                setError({\n                    message: typeof error === 'string' ? error : '发生未知错误',\n                    timestamp: Date.now()\n                });\n            }\n        }\n    }[\"useGlobalError.useCallback[handleError]\"], [\n        router\n    ]);\n    const clearError = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useGlobalError.useCallback[clearError]\": ()=>{\n            setError(null);\n            if (errorTimeoutRef.current) {\n                clearTimeout(errorTimeoutRef.current);\n                errorTimeoutRef.current = null;\n            }\n        }\n    }[\"useGlobalError.useCallback[clearError]\"], []);\n    return {\n        error,\n        handleError,\n        clearError\n    };\n}\n// 全局加载状态Hook\nfunction useGlobalLoading() {\n    const [loadingStates, setLoadingStates] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(new Map());\n    const [loadingCount, setLoadingCount] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useGlobalLoading.useEffect\": ()=>{\n            const handleLoadingChange = {\n                \"useGlobalLoading.useEffect.handleLoadingChange\": (states)=>{\n                    setLoadingStates(new Map(states));\n                    setLoadingCount(states.size);\n                }\n            }[\"useGlobalLoading.useEffect.handleLoadingChange\"];\n            const unsubscribe = _lib_api__WEBPACK_IMPORTED_MODULE_1__.loadingManager.subscribe(handleLoadingChange);\n            return ({\n                \"useGlobalLoading.useEffect\": ()=>unsubscribe()\n            })[\"useGlobalLoading.useEffect\"];\n        }\n    }[\"useGlobalLoading.useEffect\"], []);\n    const isLoading = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useGlobalLoading.useCallback[isLoading]\": (key)=>{\n            return _lib_api__WEBPACK_IMPORTED_MODULE_1__.loadingManager.isLoading(key);\n        }\n    }[\"useGlobalLoading.useCallback[isLoading]\"], []);\n    const isAnyLoading = loadingCount > 0;\n    return {\n        loadingStates,\n        loadingCount,\n        isLoading,\n        isAnyLoading\n    };\n}\n// 组合Hook：同时管理加载状态和错误\nfunction useApiState() {\n    const { error, handleError, clearError } = useGlobalError();\n    const { loadingStates, loadingCount, isLoading, isAnyLoading } = useGlobalLoading();\n    // 获取当前正在加载的请求列表\n    const getActiveRequests = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useApiState.useCallback[getActiveRequests]\": ()=>{\n            return Array.from(loadingStates.keys());\n        }\n    }[\"useApiState.useCallback[getActiveRequests]\"], [\n        loadingStates\n    ]);\n    return {\n        // 错误状态\n        error,\n        handleError,\n        clearError,\n        // 加载状态\n        loadingStates,\n        loadingCount,\n        isLoading,\n        isAnyLoading,\n        getActiveRequests\n    };\n}\n// 特定API操作的Hook，支持自动重试和取消\nfunction useApiOperation() {\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const abortControllerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    // 取消当前请求\n    const cancel = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useApiOperation.useCallback[cancel]\": ()=>{\n            if (abortControllerRef.current) {\n                abortControllerRef.current.abort();\n                abortControllerRef.current = null;\n                setLoading(false);\n            }\n        }\n    }[\"useApiOperation.useCallback[cancel]\"], []);\n    const execute = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useApiOperation.useCallback[execute]\": async (operation, options = {})=>{\n            // 取消之前的请求\n            cancel();\n            // 创建新的AbortController\n            abortControllerRef.current = new AbortController();\n            const { retries = 0, retryDelay = 1000, onSuccess, onError } = options;\n            setLoading(true);\n            setError(null);\n            let lastError = null;\n            // 重试逻辑\n            for(let attempt = 0; attempt <= retries; attempt++){\n                try {\n                    const result = await operation();\n                    if (result.success) {\n                        setData(result.data);\n                        setLoading(false);\n                        if (onSuccess) {\n                            onSuccess(result.data);\n                        }\n                        return result.data;\n                    } else {\n                        throw new Error(result.error || '操作失败');\n                    }\n                } catch (err) {\n                    lastError = err;\n                    // 如果是用户取消，不重试\n                    if (err instanceof DOMException && err.name === 'AbortError') {\n                        break;\n                    }\n                    // 判断是否应该重试\n                    const shouldRetry = attempt < retries;\n                    if (!shouldRetry) {\n                        break;\n                    }\n                    // 等待后重试\n                    console.log(`操作失败，${attempt + 1}/${retries + 1}次尝试，等待${retryDelay}ms后重试...`);\n                    await new Promise({\n                        \"useApiOperation.useCallback[execute]\": (resolve)=>setTimeout(resolve, retryDelay)\n                    }[\"useApiOperation.useCallback[execute]\"]);\n                }\n            }\n            // 所有重试都失败了\n            const errorMessage = lastError instanceof Error ? lastError.message : '操作失败';\n            const errorState = {\n                message: errorMessage,\n                timestamp: Date.now(),\n                ...lastError instanceof _lib_api__WEBPACK_IMPORTED_MODULE_1__.ApiError ? {\n                    code: lastError.code,\n                    status: lastError.status,\n                    details: lastError.details\n                } : {}\n            };\n            setError(errorState);\n            setLoading(false);\n            if (onError) {\n                onError(lastError);\n            }\n            throw lastError;\n        }\n    }[\"useApiOperation.useCallback[execute]\"], [\n        cancel\n    ]);\n    const reset = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useApiOperation.useCallback[reset]\": ()=>{\n            cancel();\n            setLoading(false);\n            setError(null);\n            setData(null);\n        }\n    }[\"useApiOperation.useCallback[reset]\"], [\n        cancel\n    ]);\n    // 清理函数\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useApiOperation.useEffect\": ()=>{\n            return ({\n                \"useApiOperation.useEffect\": ()=>{\n                    if (abortControllerRef.current) {\n                        abortControllerRef.current.abort();\n                    }\n                }\n            })[\"useApiOperation.useEffect\"];\n        }\n    }[\"useApiOperation.useEffect\"], []);\n    return {\n        loading,\n        error,\n        data,\n        execute,\n        cancel,\n        reset\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./hooks/useApiState.ts\n");

/***/ }),

/***/ "(ssr)/./lib/api.ts":
/*!********************!*\
  !*** ./lib/api.ts ***!
  \********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiError: () => (/* binding */ ApiError),\n/* harmony export */   authApi: () => (/* binding */ authApi),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   expiryApi: () => (/* binding */ expiryApi),\n/* harmony export */   foodItemApi: () => (/* binding */ foodItemApi),\n/* harmony export */   inventoryApi: () => (/* binding */ inventoryApi),\n/* harmony export */   loadingManager: () => (/* binding */ loadingManager),\n/* harmony export */   recipeApi: () => (/* binding */ recipeApi),\n/* harmony export */   utils: () => (/* binding */ utils)\n/* harmony export */ });\n// API服务层 - 处理所有后端API调用\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api';\n// 全局加载状态管理\nclass LoadingManager {\n    setLoading(key, loading) {\n        if (loading) {\n            this.loadingStates.set(key, true);\n        } else {\n            this.loadingStates.delete(key);\n        }\n        this.notifyListeners();\n    }\n    isLoading(key) {\n        if (key) {\n            return this.loadingStates.has(key);\n        }\n        return this.loadingStates.size > 0;\n    }\n    subscribe(listener) {\n        this.listeners.add(listener);\n        return ()=>this.listeners.delete(listener);\n    }\n    notifyListeners() {\n        this.listeners.forEach((listener)=>listener(new Map(this.loadingStates)));\n    }\n    constructor(){\n        this.loadingStates = new Map();\n        this.listeners = new Set();\n    }\n}\nconst loadingManager = new LoadingManager();\n// 错误类型定义\nclass ApiError extends Error {\n    constructor(message, status, code, details){\n        super(message), this.status = status, this.code = code, this.details = details;\n        this.name = 'ApiError';\n    }\n}\n// 网络错误检测\nfunction isNetworkError(error) {\n    return error instanceof TypeError && (error.message.includes('fetch') || error.message.includes('network') || error.message.includes('connection'));\n}\n// 重试延迟计算（指数退避）\nfunction calculateRetryDelay(attempt, baseDelay = 1000) {\n    return Math.min(baseDelay * Math.pow(2, attempt), 10000);\n}\n// 超时处理\nfunction withTimeout(promise, timeout) {\n    return Promise.race([\n        promise,\n        new Promise((_, reject)=>setTimeout(()=>reject(new Error('Request timeout')), timeout))\n    ]);\n}\n// 简单的内存缓存实现\nclass ApiCache {\n    get(key) {\n        const item = this.cache.get(key);\n        if (!item) return null;\n        return item.data;\n    }\n    set(key, data, ttl) {\n        this.cache.set(key, {\n            data,\n            timestamp: Date.now() + ttl\n        });\n        // 设置过期清理\n        setTimeout(()=>{\n            this.cache.delete(key);\n        }, ttl);\n    }\n    has(key) {\n        const item = this.cache.get(key);\n        if (!item) return false;\n        // 检查是否过期\n        if (Date.now() > item.timestamp) {\n            this.cache.delete(key);\n            return false;\n        }\n        return true;\n    }\n    clear() {\n        this.cache.clear();\n    }\n    constructor(){\n        this.cache = new Map();\n    }\n}\nconst apiCache = new ApiCache();\n// 请求拦截器\nconst requestInterceptors = [\n    // 添加时间戳防止缓存（仅对GET请求）\n    (config)=>{\n        if (config.method === 'GET' || !config.method) {\n            const url = new URL(config.url || '');\n            url.searchParams.append('_t', Date.now().toString());\n            config.url = url.toString();\n        }\n        return config;\n    }\n];\n// 响应拦截器\nconst responseInterceptors = [\n    // 示例：记录响应时间\n    async (response)=>{\n        console.debug(`API响应: ${response.url} - 状态: ${response.status}`);\n        return response;\n    }\n];\n// 错误拦截器\nconst errorInterceptors = [\n    // 统一错误日志\n    async (error)=>{\n        console.error('API错误:', error);\n        return Promise.reject(error);\n    }\n];\n// 增强的API请求函数，支持拦截器、缓存、重试、超时和错误处理\nasync function apiRequest(endpoint, options = {}) {\n    const { timeout = 30000, retries = 3, retryDelay = 1000, skipAuth = false, cacheTime = 0, ...fetchOptions } = options;\n    const url = `${API_BASE_URL}${endpoint}`;\n    const requestKey = `${fetchOptions.method || 'GET'}_${endpoint}`;\n    const cacheKey = `${requestKey}_${JSON.stringify(fetchOptions.body || '')}`;\n    // 检查缓存（仅对GET请求）\n    if (cacheTime > 0 && (fetchOptions.method === 'GET' || !fetchOptions.method)) {\n        if (apiCache.has(cacheKey)) {\n            return apiCache.get(cacheKey);\n        }\n    }\n    // 设置加载状态\n    loadingManager.setLoading(requestKey, true);\n    const defaultHeaders = {\n        'Content-Type': 'application/json'\n    };\n    // 添加认证token（如果存在且未跳过认证）\n    if (!skipAuth && \"undefined\" !== 'undefined') {}\n    let config = {\n        ...fetchOptions,\n        headers: {\n            ...defaultHeaders,\n            ...fetchOptions.headers\n        }\n    };\n    // 应用请求拦截器\n    let finalUrl = url;\n    for (const interceptor of requestInterceptors){\n        const configWithUrl = interceptor({\n            ...config,\n            url: finalUrl\n        });\n        finalUrl = configWithUrl.url || finalUrl;\n        config = {\n            ...configWithUrl\n        };\n        // Remove the url property from config since it's not part of RequestInit\n        delete config.url;\n    }\n    let lastError = null;\n    // 重试逻辑\n    for(let attempt = 0; attempt <= retries; attempt++){\n        try {\n            // 执行请求（带超时）\n            let response = await withTimeout(fetch(finalUrl, config), timeout);\n            // 应用响应拦截器\n            for (const interceptor of responseInterceptors){\n                response = await interceptor(response);\n            }\n            // 尝试解析响应\n            let data;\n            const contentType = response.headers.get('content-type');\n            if (contentType && contentType.includes('application/json')) {\n                data = await response.json();\n            } else {\n                // 非JSON响应，尝试获取文本\n                const text = await response.text();\n                data = {\n                    message: text\n                };\n            }\n            // 清除加载状态\n            loadingManager.setLoading(requestKey, false);\n            // 检查HTTP状态\n            if (!response.ok) {\n                const errorMessage = data.error || data.message || `HTTP ${response.status}`;\n                // 特殊处理认证错误\n                if (response.status === 401) {\n                    // Token过期或无效，清除本地认证信息\n                    if (false) {}\n                    throw new ApiError('认证已过期，请重新登录', response.status, 'AUTH_EXPIRED');\n                }\n                // 特殊处理权限错误\n                if (response.status === 403) {\n                    throw new ApiError('权限不足', response.status, 'FORBIDDEN');\n                }\n                // 特殊处理服务器错误（可重试）\n                if (response.status >= 500 && attempt < retries) {\n                    throw new ApiError(errorMessage, response.status, 'SERVER_ERROR');\n                }\n                // 客户端错误（不重试）\n                if (response.status >= 400 && response.status < 500) {\n                    const errorResponse = {\n                        success: false,\n                        error: errorMessage,\n                        details: data\n                    };\n                    // 应用错误拦截器\n                    for (const interceptor of errorInterceptors){\n                        try {\n                            await interceptor(new ApiError(errorMessage, response.status));\n                        } catch (e) {\n                            console.error('错误拦截器执行失败:', e);\n                        }\n                    }\n                    return errorResponse;\n                }\n                throw new ApiError(errorMessage, response.status);\n            }\n            // 成功响应\n            const successResponse = data.success !== undefined ? data : {\n                success: true,\n                data\n            };\n            // 缓存响应（仅对GET请求）\n            if (cacheTime > 0 && (fetchOptions.method === 'GET' || !fetchOptions.method)) {\n                apiCache.set(cacheKey, successResponse, cacheTime);\n            }\n            return successResponse;\n        } catch (error) {\n            lastError = error instanceof Error ? error : new Error('Unknown error');\n            // 记录错误\n            console.error(`API请求失败 [${endpoint}] (尝试 ${attempt + 1}/${retries + 1}):`, lastError);\n            // 应用错误拦截器\n            for (const interceptor of errorInterceptors){\n                try {\n                    await interceptor(lastError);\n                } catch (e) {\n                    console.error('错误拦截器执行失败:', e);\n                }\n            }\n            // 判断是否应该重试\n            const shouldRetry = attempt < retries && (isNetworkError(lastError) || lastError.message.includes('timeout') || lastError instanceof ApiError && lastError.status && lastError.status >= 500);\n            if (!shouldRetry) {\n                break;\n            }\n            // 等待后重试\n            if (attempt < retries) {\n                const delay = calculateRetryDelay(attempt, retryDelay);\n                console.log(`等待 ${delay}ms 后重试...`);\n                await new Promise((resolve)=>setTimeout(resolve, delay));\n            }\n        }\n    }\n    // 清除加载状态\n    loadingManager.setLoading(requestKey, false);\n    // 所有重试都失败了\n    const finalError = lastError || new Error('请求失败');\n    return {\n        success: false,\n        error: finalError instanceof ApiError ? finalError.message : '网络请求失败',\n        details: finalError instanceof ApiError ? finalError.details : undefined\n    };\n}\n// 库存管理API\nconst inventoryApi = {\n    // 获取库存列表\n    async getInventory (params) {\n        const searchParams = new URLSearchParams();\n        if (params?.status) searchParams.append('status', params.status);\n        if (params?.includeExpired) searchParams.append('includeExpired', params.includeExpired.toString());\n        if (params?.orderBy) searchParams.append('orderBy', params.orderBy);\n        if (params?.familyId) searchParams.append('familyId', params.familyId);\n        const queryString = searchParams.toString();\n        return apiRequest(`/inventory${queryString ? `?${queryString}` : ''}`);\n    },\n    // 获取库存统计\n    async getStats (familyId) {\n        const params = familyId ? `?familyId=${familyId}` : '';\n        return apiRequest(`/inventory/stats${params}`);\n    },\n    // 添加库存项目\n    async addItem (item) {\n        return apiRequest('/inventory', {\n            method: 'POST',\n            body: JSON.stringify(item)\n        });\n    },\n    // 批量添加库存项目\n    async addBatch (items, familyId) {\n        return apiRequest('/inventory/batch', {\n            method: 'POST',\n            body: JSON.stringify({\n                items,\n                family_id: familyId\n            })\n        });\n    },\n    // 更新库存项目\n    async updateItem (id, updates) {\n        return apiRequest(`/inventory/${id}`, {\n            method: 'PUT',\n            body: JSON.stringify(updates)\n        });\n    },\n    // 消费库存项目\n    async consumeItem (id, quantity) {\n        return apiRequest(`/inventory/${id}/consume`, {\n            method: 'POST',\n            body: JSON.stringify({\n                quantity\n            })\n        });\n    },\n    // 删除库存项目\n    async deleteItem (id) {\n        return apiRequest(`/inventory/${id}`, {\n            method: 'DELETE'\n        });\n    },\n    // 搜索库存\n    async searchInventory (keyword, params) {\n        const searchParams = new URLSearchParams({\n            q: keyword\n        });\n        if (params?.status) searchParams.append('status', params.status);\n        if (params?.limit) searchParams.append('limit', params.limit.toString());\n        if (params?.familyId) searchParams.append('familyId', params.familyId);\n        return apiRequest(`/inventory/search?${searchParams.toString()}`);\n    },\n    // 获取即将过期的食材\n    async getExpiringItems (days = 7, familyId) {\n        const params = new URLSearchParams({\n            days: days.toString()\n        });\n        if (familyId) params.append('familyId', familyId);\n        return apiRequest(`/inventory/expiring?${params.toString()}`);\n    },\n    // 获取已过期的食材\n    async getExpiredItems (familyId, autoUpdate = true) {\n        const params = new URLSearchParams({\n            autoUpdate: autoUpdate.toString()\n        });\n        if (familyId) params.append('familyId', familyId);\n        return apiRequest(`/inventory/expired?${params.toString()}`);\n    }\n};\n// 过期提醒API\nconst expiryApi = {\n    // 获取过期统计信息\n    async getStats (familyId) {\n        const params = familyId ? `?familyId=${familyId}` : '';\n        return apiRequest(`/expiry/stats${params}`);\n    },\n    // 检查过期提醒\n    async checkReminders (params) {\n        const searchParams = new URLSearchParams();\n        if (params?.familyId) searchParams.append('familyId', params.familyId);\n        if (params?.expiringSoonDays) searchParams.append('expiringSoonDays', params.expiringSoonDays.toString());\n        if (params?.expiringTodayDays) searchParams.append('expiringTodayDays', params.expiringTodayDays.toString());\n        const queryString = searchParams.toString();\n        return apiRequest(`/expiry/check${queryString ? `?${queryString}` : ''}`);\n    },\n    // 发送过期提醒\n    async sendReminders (params) {\n        return apiRequest('/expiry/send-reminders', {\n            method: 'POST',\n            body: JSON.stringify(params || {})\n        });\n    },\n    // 更新过期状态\n    async updateExpiredStatus (familyId) {\n        return apiRequest('/expiry/update-status', {\n            method: 'POST',\n            body: JSON.stringify({\n                familyId\n            })\n        });\n    },\n    // 获取即将过期的食材详情\n    async getExpiringItems (params) {\n        const searchParams = new URLSearchParams();\n        if (params?.days) searchParams.append('days', params.days.toString());\n        if (params?.familyId) searchParams.append('familyId', params.familyId);\n        if (params?.includeToday !== undefined) searchParams.append('includeToday', params.includeToday.toString());\n        const queryString = searchParams.toString();\n        return apiRequest(`/expiry/expiring-items${queryString ? `?${queryString}` : ''}`);\n    },\n    // 获取已过期的食材详情\n    async getExpiredItems (params) {\n        const searchParams = new URLSearchParams();\n        if (params?.familyId) searchParams.append('familyId', params.familyId);\n        if (params?.autoUpdate !== undefined) searchParams.append('autoUpdate', params.autoUpdate.toString());\n        const queryString = searchParams.toString();\n        return apiRequest(`/expiry/expired-items${queryString ? `?${queryString}` : ''}`);\n    }\n};\n// 食材基础信息API\nconst foodItemApi = {\n    // 搜索食材\n    async search (keyword, params) {\n        // 这个API需要在后端实现\n        const searchParams = new URLSearchParams({\n            q: keyword\n        });\n        if (params?.category) searchParams.append('category', params.category);\n        if (params?.limit) searchParams.append('limit', params.limit.toString());\n        return apiRequest(`/food-items/search?${searchParams.toString()}`);\n    },\n    // 获取分类列表\n    async getCategories () {\n        return apiRequest('/food-items/categories');\n    }\n};\n// 菜谱推荐API\nconst recipeApi = {\n    // 获取所有菜谱\n    async getAllRecipes (params) {\n        const searchParams = new URLSearchParams();\n        if (params?.page) searchParams.append('page', params.page.toString());\n        if (params?.limit) searchParams.append('limit', params.limit.toString());\n        if (params?.difficulty) searchParams.append('difficulty', params.difficulty);\n        if (params?.maxCookingTime) searchParams.append('maxCookingTime', params.maxCookingTime.toString());\n        if (params?.tags) searchParams.append('tags', params.tags.join(','));\n        const queryString = searchParams.toString();\n        return apiRequest(`/recipes${queryString ? `?${queryString}` : ''}`);\n    },\n    // 获取菜谱详情\n    async getRecipeById (id) {\n        return apiRequest(`/recipes/${id}`);\n    },\n    // 搜索菜谱\n    async searchRecipes (keyword, params) {\n        const searchParams = new URLSearchParams();\n        if (params?.difficulty) searchParams.append('difficulty', params.difficulty);\n        if (params?.maxCookingTime) searchParams.append('maxCookingTime', params.maxCookingTime.toString());\n        if (params?.tags) searchParams.append('tags', params.tags.join(','));\n        if (params?.limit) searchParams.append('limit', params.limit.toString());\n        const queryString = searchParams.toString();\n        return apiRequest(`/recipes/search/${encodeURIComponent(keyword)}${queryString ? `?${queryString}` : ''}`);\n    },\n    // 获取热门菜谱\n    async getPopularRecipes (params) {\n        const searchParams = new URLSearchParams();\n        if (params?.limit) searchParams.append('limit', params.limit.toString());\n        if (params?.difficulty) searchParams.append('difficulty', params.difficulty);\n        if (params?.maxCookingTime) searchParams.append('maxCookingTime', params.maxCookingTime.toString());\n        const queryString = searchParams.toString();\n        return apiRequest(`/recipes/popular${queryString ? `?${queryString}` : ''}`);\n    },\n    // 获取用户收藏的菜谱\n    async getFavoriteRecipes (params) {\n        const searchParams = new URLSearchParams();\n        if (params?.limit) searchParams.append('limit', params.limit.toString());\n        if (params?.offset) searchParams.append('offset', params.offset.toString());\n        const queryString = searchParams.toString();\n        return apiRequest(`/recipes/favorites${queryString ? `?${queryString}` : ''}`);\n    },\n    // 切换菜谱收藏状态\n    async toggleFavorite (recipeId) {\n        return apiRequest(`/recipes/${recipeId}/favorite`, {\n            method: 'POST'\n        });\n    },\n    // 根据现有食材推荐菜谱\n    async getRecipesByIngredients (ingredients, params) {\n        return apiRequest('/recipes/recommend/by-ingredients', {\n            method: 'POST',\n            body: JSON.stringify({\n                ingredients,\n                minMatchRatio: params?.minMatchRatio || 0.5,\n                limit: params?.limit || 20,\n                familyId: params?.familyId\n            })\n        });\n    },\n    // 获取临期食材优先菜谱\n    async getRecipesForExpiringIngredients (params) {\n        const searchParams = new URLSearchParams();\n        if (params?.familyId) searchParams.append('familyId', params.familyId);\n        if (params?.daysAhead) searchParams.append('daysAhead', params.daysAhead.toString());\n        if (params?.limit) searchParams.append('limit', params.limit.toString());\n        const queryString = searchParams.toString();\n        return apiRequest(`/recipes/recommend/expiring${queryString ? `?${queryString}` : ''}`);\n    },\n    // 智能菜谱推荐\n    async getSmartRecommendations (params) {\n        const searchParams = new URLSearchParams();\n        if (params?.familyId) searchParams.append('familyId', params.familyId);\n        if (params?.limit) searchParams.append('limit', params.limit.toString());\n        if (params?.includeExpiring !== undefined) searchParams.append('includeExpiring', params.includeExpiring.toString());\n        if (params?.preferredDifficulty) searchParams.append('preferredDifficulty', params.preferredDifficulty);\n        if (params?.maxCookingTime) searchParams.append('maxCookingTime', params.maxCookingTime.toString());\n        const queryString = searchParams.toString();\n        return apiRequest(`/recipes/recommend/smart${queryString ? `?${queryString}` : ''}`);\n    },\n    // 个性化菜谱推荐\n    async getPersonalizedRecommendations (params) {\n        const searchParams = new URLSearchParams();\n        if (params?.familyId) searchParams.append('familyId', params.familyId);\n        if (params?.limit) searchParams.append('limit', params.limit.toString());\n        if (params?.includeNewRecipes !== undefined) searchParams.append('includeNewRecipes', params.includeNewRecipes.toString());\n        if (params?.diversityFactor) searchParams.append('diversityFactor', params.diversityFactor.toString());\n        const queryString = searchParams.toString();\n        return apiRequest(`/recipes/recommend/personalized${queryString ? `?${queryString}` : ''}`);\n    },\n    // 混合推荐算法\n    async getHybridRecommendations (params) {\n        const searchParams = new URLSearchParams();\n        if (params?.familyId) searchParams.append('familyId', params.familyId);\n        if (params?.limit) searchParams.append('limit', params.limit.toString());\n        if (params?.contentWeight) searchParams.append('contentWeight', params.contentWeight.toString());\n        if (params?.collaborativeWeight) searchParams.append('collaborativeWeight', params.collaborativeWeight.toString());\n        if (params?.inventoryWeight) searchParams.append('inventoryWeight', params.inventoryWeight.toString());\n        if (params?.popularityWeight) searchParams.append('popularityWeight', params.popularityWeight.toString());\n        const queryString = searchParams.toString();\n        return apiRequest(`/recipes/recommend/hybrid${queryString ? `?${queryString}` : ''}`);\n    },\n    // 获取菜谱分类和标签\n    async getCategories () {\n        return apiRequest('/recipes/categories');\n    },\n    // 初始化菜谱数据\n    async initializeRecipeData () {\n        return apiRequest('/recipes/initialize', {\n            method: 'POST'\n        });\n    }\n};\n// ========== 用户认证API ==========\nconst authApi = {\n    // 用户注册\n    async register ({ username, email, password }) {\n        return apiRequest('/auth/register', {\n            method: 'POST',\n            body: JSON.stringify({\n                username,\n                email,\n                password\n            })\n        });\n    },\n    // 用户登录\n    async login ({ email, password }) {\n        return apiRequest('/auth/login', {\n            method: 'POST',\n            body: JSON.stringify({\n                email,\n                password\n            })\n        });\n    },\n    // 获取当前用户信息\n    async getMe () {\n        return apiRequest('/auth/me', {\n            method: 'GET'\n        });\n    },\n    // 刷新token\n    async refresh (token) {\n        return apiRequest('/auth/refresh', {\n            method: 'POST',\n            headers: {\n                Authorization: `Bearer ${token}`\n            }\n        });\n    }\n};\n// 工具函数\nconst utils = {\n    // 计算剩余天数\n    calculateDaysLeft (expiryDate) {\n        const today = new Date();\n        const expiry = new Date(expiryDate);\n        const diffTime = expiry.getTime() - today.getTime();\n        return Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    },\n    // 格式化日期\n    formatDate (dateString) {\n        const date = new Date(dateString);\n        return date.toLocaleDateString('zh-CN');\n    },\n    // 获取过期状态\n    getExpiryStatus (daysLeft) {\n        if (daysLeft < 0) return {\n            color: 'text-red-600',\n            bg: 'bg-red-50',\n            text: '已过期'\n        };\n        if (daysLeft <= 3) return {\n            color: 'text-orange-600',\n            bg: 'bg-orange-50',\n            text: `${daysLeft}天后过期`\n        };\n        if (daysLeft <= 7) return {\n            color: 'text-yellow-600',\n            bg: 'bg-yellow-50',\n            text: `${daysLeft}天后过期`\n        };\n        return {\n            color: 'text-green-600',\n            bg: 'bg-green-50',\n            text: `${daysLeft}天后过期`\n        };\n    },\n    // 转换后端数据格式到前端格式\n    transformInventoryItem (item) {\n        return {\n            id: item.id,\n            name: item.food_name || item.name,\n            category: item.category,\n            quantity: item.quantity,\n            unit: item.unit,\n            purchaseDate: item.added_date.split('T')[0],\n            expiryDate: item.expiry_date.split('T')[0],\n            daysLeft: this.calculateDaysLeft(item.expiry_date),\n            location: item.location,\n            notes: item.notes,\n            status: item.status\n        };\n    },\n    // 转换前端数据格式到后端格式\n    transformToBackendFormat (item) {\n        return {\n            name: item.name,\n            category: item.category,\n            quantity: item.quantity,\n            unit: item.unit,\n            expiry_date: new Date(item.expiryDate).toISOString(),\n            location: item.location,\n            notes: item.notes\n        };\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n    inventoryApi,\n    expiryApi,\n    foodItemApi,\n    recipeApi,\n    authApi,\n    utils\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/api.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5Cjames%5CDesktop%5Cbingxiang%5Creaddy%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjames%5CDesktop%5Cbingxiang%5Creaddy&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();