{"timestamp":"2025-07-17T04:03:05.285Z","level":"INFO","message":"Server starting up","port":"3001","environment":"development","nodeVersion":"v22.16.0","platform":"win32","pid":19868,"memory":{"rss":126525440,"heapTotal":30994432,"heapUsed":11909752,"external":2167136,"arrayBuffers":18835},"uptime":0.6940691}
{"timestamp":"2025-07-17T04:03:05.289Z","level":"INFO","message":"Server started successfully","port":"3001","pid":19868,"memory":{"rss":126562304,"heapTotal":30994432,"heapUsed":11999440,"external":2186267,"arrayBuffers":18835},"uptime":0.697109}
{"timestamp":"2025-07-17T04:03:56.597Z","level":"ALERT","message":"High memory usage: 89%","type":"memory","severity":"warning","threshold":80,"current":89,"pid":19868,"memory":{"rss":117014528,"heapTotal":13692928,"heapUsed":12246952,"external":2186083,"arrayBuffers":18651},"uptime":52.0055286}
{"timestamp":"2025-07-17T04:03:56.598Z","level":"ALERT","message":"Database connection failed","type":"database","severity":"critical","details":{"status":"unhealthy","message":"Database connection failed","duration":"1ms","details":{"error":"getDatabase is not a function"}},"pid":19868,"memory":{"rss":117014528,"heapTotal":13692928,"heapUsed":12251432,"external":2186083,"arrayBuffers":18651},"uptime":52.0062625}
{"timestamp":"2025-07-17T04:04:17.988Z","level":"ALERT","message":"High memory usage: 85%","type":"memory","severity":"warning","threshold":80,"current":85,"pid":19868,"memory":{"rss":118341632,"heapTotal":15003648,"heapUsed":12748928,"external":2186197,"arrayBuffers":18651},"uptime":73.395511}
{"timestamp":"2025-07-17T04:04:17.988Z","level":"ALERT","message":"High error rate: 50.00%","type":"error_rate","severity":"critical","threshold":5,"current":50,"pid":19868,"memory":{"rss":118341632,"heapTotal":15003648,"heapUsed":12752272,"external":2186197,"arrayBuffers":18651},"uptime":73.3959016}
{"timestamp":"2025-07-17T04:04:17.989Z","level":"ALERT","message":"Database connection failed","type":"database","severity":"critical","details":{"status":"unhealthy","message":"Database connection failed","duration":"1ms","details":{"error":"getDatabase is not a function"}},"pid":19868,"memory":{"rss":118341632,"heapTotal":15003648,"heapUsed":12755176,"external":2186197,"arrayBuffers":18651},"uptime":73.3962307}
{"timestamp":"2025-07-17T04:05:52.393Z","level":"ALERT","message":"High memory usage: 86%","type":"memory","severity":"warning","threshold":80,"current":86,"pid":19868,"memory":{"rss":118419456,"heapTotal":15003648,"heapUsed":12898072,"external":2186197,"arrayBuffers":18651},"uptime":167.8011657}
{"timestamp":"2025-07-17T04:05:52.394Z","level":"ALERT","message":"High error rate: 60.00%","type":"error_rate","severity":"critical","threshold":5,"current":60,"pid":19868,"memory":{"rss":118419456,"heapTotal":15003648,"heapUsed":12900896,"external":2186197,"arrayBuffers":18651},"uptime":167.8015827}
{"timestamp":"2025-07-17T04:05:52.394Z","level":"ALERT","message":"Database connection failed","type":"database","severity":"critical","details":{"status":"unhealthy","message":"Database connection failed","duration":"0ms","details":{"error":"getDatabase is not a function"}},"pid":19868,"memory":{"rss":118423552,"heapTotal":15003648,"heapUsed":12903872,"external":2186197,"arrayBuffers":18651},"uptime":167.8020054}
{"timestamp":"2025-07-17T04:08:05.290Z","level":"ALERT","message":"High memory usage: 85%","type":"memory","severity":"warning","threshold":80,"current":85,"pid":19868,"memory":{"rss":118714368,"heapTotal":15265792,"heapUsed":12986096,"external":2186197,"arrayBuffers":18651},"uptime":300.6972244}
{"timestamp":"2025-07-17T04:08:05.290Z","level":"ALERT","message":"High error rate: 66.67%","type":"error_rate","severity":"critical","threshold":5,"current":66.67,"pid":19868,"memory":{"rss":118714368,"heapTotal":15265792,"heapUsed":12989728,"external":2186197,"arrayBuffers":18651},"uptime":300.6975708}
{"timestamp":"2025-07-17T04:08:05.290Z","level":"ALERT","message":"Database connection failed","type":"database","severity":"critical","details":{"status":"unhealthy","message":"Database connection failed","duration":"0ms","details":{"error":"getDatabase is not a function"}},"pid":19868,"memory":{"rss":118714368,"heapTotal":15265792,"heapUsed":12992896,"external":2186197,"arrayBuffers":18651},"uptime":300.6978235}
{"timestamp":"2025-07-17T04:08:08.599Z","level":"INFO","message":"Server shutting down","signal":"SIGINT","pid":19868,"memory":{"rss":118718464,"heapTotal":15265792,"heapUsed":12999560,"external":2186197,"arrayBuffers":18651},"uptime":304.0065784}
{"timestamp":"2025-07-17T04:08:24.423Z","level":"INFO","message":"Server starting up","port":"3001","environment":"development","nodeVersion":"v22.16.0","platform":"win32","pid":27340,"memory":{"rss":126660608,"heapTotal":30994432,"heapUsed":11855392,"external":2167136,"arrayBuffers":18835},"uptime":0.5056032}
{"timestamp":"2025-07-17T04:08:24.428Z","level":"INFO","message":"Server started successfully","port":"3001","pid":27340,"memory":{"rss":126697472,"heapTotal":30994432,"heapUsed":11945728,"external":2186267,"arrayBuffers":18835},"uptime":0.5093404}
{"timestamp":"2025-07-17T04:13:24.436Z","level":"ALERT","message":"High memory usage: 85%","type":"memory","severity":"warning","threshold":80,"current":85,"pid":27340,"memory":{"rss":116727808,"heapTotal":13955072,"heapUsed":11875336,"external":2186083,"arrayBuffers":18651},"uptime":300.516151}
{"timestamp":"2025-07-17T04:18:24.446Z","level":"ALERT","message":"High memory usage: 85%","type":"memory","severity":"warning","threshold":80,"current":85,"pid":27340,"memory":{"rss":116756480,"heapTotal":13955072,"heapUsed":11904112,"external":2186083,"arrayBuffers":18651},"uptime":600.5262294}
{"timestamp":"2025-07-17T04:23:24.453Z","level":"ALERT","message":"High memory usage: 85%","type":"memory","severity":"warning","threshold":80,"current":85,"pid":27340,"memory":{"rss":116805632,"heapTotal":13955072,"heapUsed":11930896,"external":2186083,"arrayBuffers":18651},"uptime":900.5329004}
{"timestamp":"2025-07-17T04:28:24.459Z","level":"ALERT","message":"High memory usage: 86%","type":"memory","severity":"warning","threshold":80,"current":86,"pid":27340,"memory":{"rss":116813824,"heapTotal":13955072,"heapUsed":11969080,"external":2186083,"arrayBuffers":18651},"uptime":1200.5393005}
{"timestamp":"2025-07-17T04:33:24.465Z","level":"ALERT","message":"High memory usage: 86%","type":"memory","severity":"warning","threshold":80,"current":86,"pid":27340,"memory":{"rss":116817920,"heapTotal":13955072,"heapUsed":11995928,"external":2186083,"arrayBuffers":18651},"uptime":1500.5451728}
{"timestamp":"2025-07-17T04:38:24.470Z","level":"ALERT","message":"High memory usage: 86%","type":"memory","severity":"warning","threshold":80,"current":86,"pid":27340,"memory":{"rss":116883456,"heapTotal":13955072,"heapUsed":12022536,"external":2186083,"arrayBuffers":18651},"uptime":1800.5503459}
{"timestamp":"2025-07-17T04:40:28.885Z","level":"INFO","message":"Server shutting down","signal":"SIGINT","pid":27340,"memory":{"rss":116887552,"heapTotal":13955072,"heapUsed":12031928,"external":2186083,"arrayBuffers":18651},"uptime":1924.9656971}
{"timestamp":"2025-07-17T06:03:27.466Z","level":"INFO","message":"Server starting up","port":"3001","environment":"development","nodeVersion":"v22.16.0","platform":"win32","pid":19808,"memory":{"rss":126484480,"heapTotal":30732288,"heapUsed":11858528,"external":2167136,"arrayBuffers":18835},"uptime":0.6091173}
{"timestamp":"2025-07-17T06:03:27.470Z","level":"INFO","message":"Server started successfully","port":"3001","pid":19808,"memory":{"rss":126529536,"heapTotal":30732288,"heapUsed":11948680,"external":2186267,"arrayBuffers":18835},"uptime":0.6124249}
{"timestamp":"2025-07-17T06:03:52.817Z","level":"ALERT","message":"High memory usage: 89%","type":"memory","severity":"warning","threshold":80,"current":89,"pid":19808,"memory":{"rss":117125120,"heapTotal":13692928,"heapUsed":12284848,"external":2186083,"arrayBuffers":18651},"uptime":25.9593267}
{"timestamp":"2025-07-17T06:08:27.473Z","level":"ALERT","message":"High memory usage: 89%","type":"memory","severity":"warning","threshold":80,"current":89,"pid":19808,"memory":{"rss":119894016,"heapTotal":15003648,"heapUsed":13346080,"external":2190251,"arrayBuffers":22819},"uptime":300.6146557}
{"timestamp":"2025-07-17T06:08:27.473Z","level":"ALERT","message":"High error rate: 50.00%","type":"error_rate","severity":"critical","threshold":5,"current":50,"pid":19808,"memory":{"rss":119894016,"heapTotal":15003648,"heapUsed":13349032,"external":2190251,"arrayBuffers":22819},"uptime":300.6150537}
{"timestamp":"2025-07-17T07:21:33.990Z","level":"INFO","message":"Server starting up","port":"3001","environment":"development","nodeVersion":"v22.16.0","platform":"win32","pid":2956,"memory":{"rss":74121216,"heapTotal":34402304,"heapUsed":14779408,"external":2549297,"arrayBuffers":20539},"uptime":0.7072922}
{"timestamp":"2025-07-17T07:21:33.991Z","level":"INFO","message":"Server started successfully","port":"3001","pid":2956,"memory":{"rss":74129408,"heapTotal":34402304,"heapUsed":14796264,"external":2549297,"arrayBuffers":20539},"uptime":0.7084253}
{"timestamp":"2025-07-17T07:21:34.026Z","level":"ERROR","message":"Unexpected token 'i', \"invalid json\" is not valid JSON","stack":"SyntaxError: Unexpected token 'i', \"invalid json\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (C:\\Users\\<USER>\\Desktop\\bingxiang\\backend\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (C:\\Users\\<USER>\\Desktop\\bingxiang\\backend\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at C:\\Users\\<USER>\\Desktop\\bingxiang\\backend\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (C:\\Users\\<USER>\\Desktop\\bingxiang\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Desktop\\bingxiang\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Desktop\\bingxiang\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)","statusCode":400,"url":"/api/inventory","method":"POST","ip":"::ffff:127.0.0.1","pid":2956,"memory":{"rss":74756096,"heapTotal":34402304,"heapUsed":16669696,"external":2556054,"arrayBuffers":27336},"uptime":0.7430836}
{"timestamp":"2025-07-17T07:23:28.339Z","level":"INFO","message":"Server starting up","port":"3001","environment":"development","nodeVersion":"v22.16.0","platform":"win32","pid":32372,"memory":{"rss":66621440,"heapTotal":31256576,"heapUsed":11949312,"external":2193467,"arrayBuffers":19011},"uptime":0.5728609}
{"timestamp":"2025-07-17T07:23:28.343Z","level":"INFO","message":"Server started successfully","port":"3001","pid":32372,"memory":{"rss":66752512,"heapTotal":31256576,"heapUsed":12038544,"external":2212598,"arrayBuffers":19011},"uptime":0.5756892}
{"timestamp":"2025-07-17T07:38:16.107Z","level":"INFO","message":"Server starting up","port":"3001","environment":"development","nodeVersion":"v22.16.0","platform":"win32","pid":5768,"memory":{"rss":68608000,"heapTotal":31518720,"heapUsed":11946832,"external":2193467,"arrayBuffers":19011},"uptime":0.5638387}
{"timestamp":"2025-07-17T07:38:16.113Z","level":"INFO","message":"Server started successfully","port":"3001","pid":5768,"memory":{"rss":68714496,"heapTotal":31518720,"heapUsed":12036064,"external":2212598,"arrayBuffers":19011},"uptime":0.5687982}
{"timestamp":"2025-07-17T07:43:26.133Z","level":"ALERT","message":"High memory usage: 89%","type":"memory","severity":"warning","threshold":80,"current":89,"pid":5768,"memory":{"rss":67948544,"heapTotal":18305024,"heapUsed":15749480,"external":3571426,"arrayBuffers":72054},"uptime":310.5882498}
