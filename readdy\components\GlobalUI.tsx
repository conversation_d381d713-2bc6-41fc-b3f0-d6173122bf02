'use client';

import { useCallback } from 'react';
import GlobalLoadingIndicator from './GlobalLoadingIndicator';
import GlobalErrorNotification from './GlobalErrorNotification';
import { useGlobalError, useGlobalLoading } from '@/hooks/useApiState';
import { loadingManager } from '@/lib/api';

// useGlobalUI Hook - 提供全局UI控制功能
export function useGlobalUI() {
  const { handleError, clearError } = useGlobalError();
  const { isLoading } = useGlobalLoading();

  // 显示错误消息
  const showError = useCallback((message: string, details?: any) => {
    const error = new Error(message);
    if (details) {
      (error as any).details = details;
    }
    handleError(error);
  }, [handleError]);

  // 显示加载状态
  const showLoading = useCallback((show: boolean, key: string = 'global') => {
    if (show) {
      loadingManager.setLoading(key, true);
    } else {
      loadingManager.setLoading(key, false);
    }
  }, []);

  // 隐藏错误消息
  const hideError = useCallback(() => {
    clearError();
  }, [clearError]);

  // 检查是否正在加载
  const isLoadingState = useCallback((key?: string) => {
    return isLoading(key);
  }, [isLoading]);

  return {
    showError,
    showLoading,
    hideError,
    isLoading: isLoadingState,
  };
}

export default function GlobalUI() {
  return (
    <>
      <GlobalLoadingIndicator />
      <GlobalErrorNotification />
    </>
  );
}